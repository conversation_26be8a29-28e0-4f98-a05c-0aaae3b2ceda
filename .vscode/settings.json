{"typescript.preferences.importModuleSpecifier": "relative", "editor.codeActionsOnSave": {"source.fixAll.eslint": "explicit", "source.organizeImports": "explicit"}, "editor.formatOnSave": true, "editor.defaultFormatter": "esbenp.prettier-vscode", "files.autoSave": "onFocusChange", "typescript.updateImportsOnFileMove.enabled": "always", "editor.inlayHints.enabled": "on", "typescript.inlayHints.parameterNames.enabled": "all", "typescript.inlayHints.variableTypes.enabled": true, "typescript.inlayHints.functionLikeReturnTypes.enabled": true, "eslint.workingDirectories": ["./"], "search.exclude": {"**/node_modules": true, "**/dist": true, "**/build": true, "**/.next": true}}